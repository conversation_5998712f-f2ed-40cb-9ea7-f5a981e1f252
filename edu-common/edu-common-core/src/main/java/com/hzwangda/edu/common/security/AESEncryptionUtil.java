package com.hzwangda.edu.common.security;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * AES-256加密工具类
 * 用于敏感数据的加密和解密
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Component
public class AESEncryptionUtil {

    private static final Logger logger = LoggerFactory.getLogger(AESEncryptionUtil.class);

    // AES加密算法
    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/GCM/NoPadding";
    private static final int GCM_IV_LENGTH = 12; // GCM模式推荐的IV长度
    private static final int GCM_TAG_LENGTH = 16; // GCM模式的认证标签长度

    @Value("${hky.security.encryption.key:}")
    private String encryptionKey;

    @Value("${hky.security.encryption.enabled:false}")
    private boolean encryptionEnabled;

    /**
     * 生成AES密钥
     */
    public static String generateKey() {
        try {
            KeyGenerator keyGenerator = KeyGenerator.getInstance(ALGORITHM);
            keyGenerator.init(256); // AES-256
            SecretKey secretKey = keyGenerator.generateKey();
            return Base64.getEncoder().encodeToString(secretKey.getEncoded());
        } catch (Exception e) {
            logger.error("生成AES密钥失败", e);
            throw new RuntimeException("生成AES密钥失败", e);
        }
    }

    /**
     * 加密字符串
     *
     * @param plainText 明文
     * @return 加密后的Base64字符串
     */
    public String encrypt(String plainText) {
        if (!encryptionEnabled || plainText == null || plainText.isEmpty()) {
            return plainText;
        }

        try {
            SecretKeySpec secretKey = getSecretKey();
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);

            // 生成随机IV
            byte[] iv = new byte[GCM_IV_LENGTH];
            new SecureRandom().nextBytes(iv);
            GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);

            cipher.init(Cipher.ENCRYPT_MODE, secretKey, gcmParameterSpec);
            byte[] encryptedData = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));

            // 将IV和加密数据合并
            byte[] encryptedWithIv = new byte[GCM_IV_LENGTH + encryptedData.length];
            System.arraycopy(iv, 0, encryptedWithIv, 0, GCM_IV_LENGTH);
            System.arraycopy(encryptedData, 0, encryptedWithIv, GCM_IV_LENGTH, encryptedData.length);

            return Base64.getEncoder().encodeToString(encryptedWithIv);

        } catch (Exception e) {
            logger.error("加密失败: plainText length={}", plainText.length(), e);
            throw new RuntimeException("数据加密失败", e);
        }
    }

    /**
     * 解密字符串
     *
     * @param encryptedText 加密的Base64字符串
     * @return 解密后的明文
     */
    public String decrypt(String encryptedText) {
        if (!encryptionEnabled || encryptedText == null || encryptedText.isEmpty()) {
            return encryptedText;
        }

        try {
            SecretKeySpec secretKey = getSecretKey();
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);

            byte[] encryptedWithIv = Base64.getDecoder().decode(encryptedText);

            // 分离IV和加密数据
            byte[] iv = new byte[GCM_IV_LENGTH];
            byte[] encryptedData = new byte[encryptedWithIv.length - GCM_IV_LENGTH];
            System.arraycopy(encryptedWithIv, 0, iv, 0, GCM_IV_LENGTH);
            System.arraycopy(encryptedWithIv, GCM_IV_LENGTH, encryptedData, 0, encryptedData.length);

            GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, gcmParameterSpec);

            byte[] decryptedData = cipher.doFinal(encryptedData);
            return new String(decryptedData, StandardCharsets.UTF_8);

        } catch (Exception e) {
            logger.error("解密失败: encryptedText length={}", encryptedText.length(), e);
            throw new RuntimeException("数据解密失败", e);
        }
    }

    /**
     * 批量加密
     *
     * @param plainTexts 明文数组
     * @return 加密后的字符串数组
     */
    public String[] encryptBatch(String... plainTexts) {
        if (plainTexts == null) {
            return null;
        }

        String[] encryptedTexts = new String[plainTexts.length];
        for (int i = 0; i < plainTexts.length; i++) {
            encryptedTexts[i] = encrypt(plainTexts[i]);
        }
        return encryptedTexts;
    }

    /**
     * 批量解密
     *
     * @param encryptedTexts 加密的字符串数组
     * @return 解密后的明文数组
     */
    public String[] decryptBatch(String... encryptedTexts) {
        if (encryptedTexts == null) {
            return null;
        }

        String[] plainTexts = new String[encryptedTexts.length];
        for (int i = 0; i < encryptedTexts.length; i++) {
            plainTexts[i] = decrypt(encryptedTexts[i]);
        }
        return plainTexts;
    }

    /**
     * 检查字符串是否已加密
     *
     * @param text 待检查的字符串
     * @return 是否已加密
     */
    public boolean isEncrypted(String text) {
        if (text == null || text.isEmpty()) {
            return false;
        }

        try {
            // 尝试Base64解码
            byte[] decoded = Base64.getDecoder().decode(text);
            // 检查长度是否合理（至少包含IV + 最小加密数据 + 认证标签）
            return decoded.length >= (GCM_IV_LENGTH + GCM_TAG_LENGTH + 1);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取密钥
     */
    private SecretKeySpec getSecretKey() {
        if (encryptionKey == null || encryptionKey.isEmpty()) {
            throw new IllegalStateException("加密密钥未配置，请设置 hky.security.encryption.key");
        }

        try {
            byte[] keyBytes = Base64.getDecoder().decode(encryptionKey);
            if (keyBytes.length != 32) { // AES-256需要32字节密钥
                throw new IllegalArgumentException("AES-256密钥长度必须为32字节");
            }
            return new SecretKeySpec(keyBytes, ALGORITHM);
        } catch (Exception e) {
            logger.error("密钥解析失败", e);
            throw new RuntimeException("密钥解析失败", e);
        }
    }

    /**
     * 验证加密配置
     */
    public boolean validateConfiguration() {
        try {
            if (!encryptionEnabled) {
                logger.warn("加密功能已禁用");
                return true;
            }

            if (encryptionKey == null || encryptionKey.isEmpty()) {
                logger.error("加密密钥未配置");
                return false;
            }

            // 测试加密解密
            String testText = "test_encryption_" + System.currentTimeMillis();
            String encrypted = encrypt(testText);
            String decrypted = decrypt(encrypted);

            boolean valid = testText.equals(decrypted);
            if (valid) {
                logger.info("加密配置验证成功");
            } else {
                logger.error("加密配置验证失败：加密解密结果不一致");
            }

            return valid;

        } catch (Exception e) {
            logger.error("加密配置验证失败", e);
            return false;
        }
    }

    /**
     * 获取加密状态
     */
    public boolean isEncryptionEnabled() {
        return encryptionEnabled;
    }
}
