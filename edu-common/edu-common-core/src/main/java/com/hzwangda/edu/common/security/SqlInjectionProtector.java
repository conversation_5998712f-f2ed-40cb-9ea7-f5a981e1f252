package com.hzwangda.edu.common.security;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * SQL注入防护工具类
 * 用于检测和防护SQL注入攻击
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Component
public class SqlInjectionProtector {

    private static final Logger logger = LoggerFactory.getLogger(SqlInjectionProtector.class);

    // SQL注入关键词（不区分大小写）
    private static final List<String> SQL_INJECTION_KEYWORDS = Arrays.asList(
        "select", "insert", "update", "delete", "drop", "create", "alter", "exec", "execute",
        "union", "join", "declare", "master", "truncate", "char", "and", "or", "order", "group",
        "having", "count", "mid", "substring", "substr", "length", "len", "database",
        "table", "column", "schema", "information_schema", "sys", "admin", "root", "password",
        "script", "javascript", "vbscript", "onload", "onerror", "onclick", "alert", "confirm",
        "prompt", "eval", "expression", "applet", "object", "embed", "form", "iframe", "frame",
        "meta", "link", "style", "title", "base", "bgsound", "blink", "body", "br", "div",
        "fieldset", "form", "frame", "frameset", "h1", "head", "iframe", "img", "input",
        "isindex", "link", "meta", "ol", "option", "param", "plaintext", "pre",
        "script", "select", "style", "tbody", "td", "textarea", "tfoot", "th",
        "thead", "title", "tr", "ul", "var", "xmp"
    );

    // SQL注入特殊字符
    private static final List<String> SQL_INJECTION_CHARS = Arrays.asList(
        "'", "\"", "/*", "*/", "@@", "@", "char", "nchar", "varchar", "nvarchar",
        "alter", "begin", "cast", "create", "cursor", "declare", "delete", "drop", "end",
        "exec", "execute", "fetch", "insert", "kill", "open", "select", "sys", "sysobjects",
        "syscolumns", "table", "update"
    );

    // SQL注入正则表达式模式
    private static final List<Pattern> SQL_INJECTION_PATTERNS = Arrays.asList(
        Pattern.compile("('.+(or|and).+?=.+?)|(.*?(or|and).+?=.*?)", Pattern.CASE_INSENSITIVE),
        Pattern.compile("'.+?(or|and).+?'", Pattern.CASE_INSENSITIVE),
        Pattern.compile("(union.*?select.*?from.*?)|(select.*?from.*?union.*?)", Pattern.CASE_INSENSITIVE),
        Pattern.compile("(insert.*?into.*?values.*?)|(update.*?set.*?)|(delete.*?from.*?)", Pattern.CASE_INSENSITIVE),
        Pattern.compile("(drop.*?table.*?)|(create.*?table.*?)|(alter.*?table.*?)", Pattern.CASE_INSENSITIVE),
        Pattern.compile("(exec.*?\\(.*?\\))|(execute.*?\\(.*?\\))", Pattern.CASE_INSENSITIVE),
        Pattern.compile("(script.*?>.*?</script>)|(javascript:.*?)|(vbscript:.*?)", Pattern.CASE_INSENSITIVE),
        Pattern.compile("(<.*?script.*?>.*?</.*?script.*?>)|(<.*?object.*?>.*?</.*?object.*?>)", Pattern.CASE_INSENSITIVE),
        Pattern.compile("(onload.*?=.*?)|(onerror.*?=.*?)|(onclick.*?=.*?)", Pattern.CASE_INSENSITIVE)
    );

    // XSS攻击模式
    private static final List<Pattern> XSS_PATTERNS = Arrays.asList(
        Pattern.compile("<script[^>]*>.*?</script>", Pattern.CASE_INSENSITIVE | Pattern.DOTALL),
        Pattern.compile("<iframe[^>]*>.*?</iframe>", Pattern.CASE_INSENSITIVE | Pattern.DOTALL),
        Pattern.compile("<object[^>]*>.*?</object>", Pattern.CASE_INSENSITIVE | Pattern.DOTALL),
        Pattern.compile("<embed[^>]*>.*?</embed>", Pattern.CASE_INSENSITIVE | Pattern.DOTALL),
        Pattern.compile("javascript:", Pattern.CASE_INSENSITIVE),
        Pattern.compile("vbscript:", Pattern.CASE_INSENSITIVE),
        Pattern.compile("onload\\s*=", Pattern.CASE_INSENSITIVE),
        Pattern.compile("onerror\\s*=", Pattern.CASE_INSENSITIVE),
        Pattern.compile("onclick\\s*=", Pattern.CASE_INSENSITIVE),
        Pattern.compile("onmouseover\\s*=", Pattern.CASE_INSENSITIVE)
    );

    /**
     * 检查字符串是否包含SQL注入攻击
     *
     * @param input 输入字符串
     * @return 是否包含SQL注入
     */
    public boolean containsSqlInjection(String input) {
        if (!StringUtils.hasText(input)) {
            return false;
        }

        String lowerInput = input.toLowerCase().trim();

        // 检查SQL注入关键词
        for (String keyword : SQL_INJECTION_KEYWORDS) {
            if (lowerInput.contains(keyword.toLowerCase())) {
                logger.warn("检测到SQL注入关键词: {} in input: {}", keyword, input);
                return true;
            }
        }

        // 检查SQL注入特殊字符组合
        for (String chars : SQL_INJECTION_CHARS) {
            if (lowerInput.contains(chars.toLowerCase())) {
                logger.warn("检测到SQL注入特殊字符: {} in input: {}", chars, input);
                return true;
            }
        }

        // 检查SQL注入正则表达式模式
        for (Pattern pattern : SQL_INJECTION_PATTERNS) {
            if (pattern.matcher(input).find()) {
                logger.warn("检测到SQL注入模式: {} in input: {}", pattern.pattern(), input);
                return true;
            }
        }

        return false;
    }

    /**
     * 检查字符串是否包含XSS攻击
     *
     * @param input 输入字符串
     * @return 是否包含XSS攻击
     */
    public boolean containsXss(String input) {
        if (!StringUtils.hasText(input)) {
            return false;
        }

        // 检查XSS攻击模式
        for (Pattern pattern : XSS_PATTERNS) {
            if (pattern.matcher(input).find()) {
                logger.warn("检测到XSS攻击模式: {} in input: {}", pattern.pattern(), input);
                return true;
            }
        }

        return false;
    }

    /**
     * 清理SQL注入攻击字符
     *
     * @param input 输入字符串
     * @return 清理后的字符串
     */
    public String cleanSqlInjection(String input) {
        if (!StringUtils.hasText(input)) {
            return input;
        }

        String cleaned = input;

        // 移除SQL注入关键词
        for (String keyword : SQL_INJECTION_KEYWORDS) {
            cleaned = cleaned.replaceAll("(?i)" + Pattern.quote(keyword), "");
        }

        // 移除SQL注入特殊字符
        for (String chars : SQL_INJECTION_CHARS) {
            cleaned = cleaned.replace(chars, "");
        }

        // 移除多余的空格
        cleaned = cleaned.replaceAll("\\s+", " ").trim();

        if (!cleaned.equals(input)) {
            logger.info("清理SQL注入字符: {} -> {}", input, cleaned);
        }

        return cleaned;
    }

    /**
     * 清理XSS攻击字符
     *
     * @param input 输入字符串
     * @return 清理后的字符串
     */
    public String cleanXss(String input) {
        if (!StringUtils.hasText(input)) {
            return input;
        }

        String cleaned = input;

        // 移除XSS攻击模式
        for (Pattern pattern : XSS_PATTERNS) {
            cleaned = pattern.matcher(cleaned).replaceAll("");
        }

        // 转义HTML特殊字符
        cleaned = cleaned.replace("<", "&lt;")
                        .replace(">", "&gt;")
                        .replace("\"", "&quot;")
                        .replace("'", "&#x27;")
                        .replace("/", "&#x2F;");

        if (!cleaned.equals(input)) {
            logger.info("清理XSS攻击字符: {} -> {}", input, cleaned);
        }

        return cleaned;
    }

    /**
     * 验证输入参数安全性
     *
     * @param input 输入字符串
     * @return 验证结果
     */
    public ValidationResult validateInput(String input) {
        ValidationResult result = new ValidationResult();
        result.setOriginalInput(input);
        result.setValid(true);

        if (!StringUtils.hasText(input)) {
            result.setCleanedInput(input);
            return result;
        }

        // 检查SQL注入
        if (containsSqlInjection(input)) {
            result.setValid(false);
            result.setSqlInjectionDetected(true);
            result.addRisk("检测到SQL注入攻击");
        }

        // 检查XSS攻击
        if (containsXss(input)) {
            result.setValid(false);
            result.setXssDetected(true);
            result.addRisk("检测到XSS攻击");
        }

        // 清理输入
        String cleaned = input;
        if (result.isSqlInjectionDetected()) {
            cleaned = cleanSqlInjection(cleaned);
        }
        if (result.isXssDetected()) {
            cleaned = cleanXss(cleaned);
        }

        result.setCleanedInput(cleaned);
        return result;
    }

    /**
     * 批量验证输入参数
     *
     * @param inputs 输入字符串数组
     * @return 验证结果数组
     */
    public ValidationResult[] validateInputs(String... inputs) {
        if (inputs == null) {
            return new ValidationResult[0];
        }

        ValidationResult[] results = new ValidationResult[inputs.length];
        for (int i = 0; i < inputs.length; i++) {
            results[i] = validateInput(inputs[i]);
        }

        return results;
    }

    /**
     * 验证结果类
     */
    @lombok.Data
    public static class ValidationResult {
        private String originalInput;
        private String cleanedInput;
        private boolean valid;
        private boolean sqlInjectionDetected;
        private boolean xssDetected;
        private List<String> risks = new java.util.ArrayList<>();

        public void addRisk(String risk) {
            if (this.risks == null) {
                this.risks = new java.util.ArrayList<>();
            }
            this.risks.add(risk);
        }

        @Override
        public String toString() {
            return "ValidationResult{" +
                    "valid=" + valid +
                    ", sqlInjectionDetected=" + sqlInjectionDetected +
                    ", xssDetected=" + xssDetected +
                    ", risks=" + risks +
                    '}';
        }
    }
}
