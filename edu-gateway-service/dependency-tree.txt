[INFO] Scanning for projects...
[INFO] 
[INFO] ----------------< com.hzwangda.edu:edu-gateway-service >----------------
[INFO] Building API网关服务 1.0.0-SNAPSHOT
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
Downloading from HzWangdaRepo: https://mvnrepository.hzwangda.com/repository/maven-snapshots/com/hzwangda/edu/edu-common-web/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 780 B
                   
Downloaded from HzWangdaRepo: https://mvnrepository.hzwangda.com/repository/maven-snapshots/com/hzwangda/edu/edu-common-web/1.0.0-SNAPSHOT/maven-metadata.xml (780 B at 1.5 kB/s)
Downloading from HzWangdaRepo: https://mvnrepository.hzwangda.com/repository/maven-snapshots/com/hzwangda/edu/edu-common-services/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 612 B
                   
Downloaded from HzWangdaRepo: https://mvnrepository.hzwangda.com/repository/maven-snapshots/com/hzwangda/edu/edu-common-services/1.0.0-SNAPSHOT/maven-metadata.xml (612 B at 17 kB/s)
Downloading from HzWangdaRepo: https://mvnrepository.hzwangda.com/repository/maven-snapshots/com/hzwangda/edu/edu-common-core/1.0.0-SNAPSHOT/maven-metadata.xml
Progress (1): 781 B
                   
Downloaded from HzWangdaRepo: https://mvnrepository.hzwangda.com/repository/maven-snapshots/com/hzwangda/edu/edu-common-core/1.0.0-SNAPSHOT/maven-metadata.xml (781 B at 26 kB/s)
[INFO] 
[INFO] --- dependency:3.7.0:tree (default-cli) @ edu-gateway-service ---
[INFO] com.hzwangda.edu:edu-gateway-service:jar:1.0.0-SNAPSHOT
[INFO] +- com.hzwangda.edu:edu-common-web:jar:1.0.0-SNAPSHOT:compile
[INFO] |  +- com.hzwangda.edu:edu-common-core:jar:1.0.0-SNAPSHOT:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-validation:jar:3.4.7:compile
[INFO] |  |  |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:10.1.42:compile
[INFO] |  |  |  \- org.hibernate.validator:hibernate-validator:jar:8.0.2.Final:compile
[INFO] |  |  |     +- jakarta.validation:jakarta.validation-api:jar:3.0.2:compile
[INFO] |  |  |     +- org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile
[INFO] |  |  |     \- com.fasterxml:classmate:jar:1.7.0:compile
[INFO] |  |  +- cn.hutool:hutool-all:jar:5.8.37:compile
[INFO] |  |  +- com.alibaba.fastjson2:fastjson2:jar:2.0.57:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-data-redis:jar:3.4.7:compile
[INFO] |  |  +- org.projectlombok:lombok:jar:1.18.38:compile (optional)
[INFO] |  |  \- org.mapstruct:mapstruct:jar:1.6.3:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-webflux:jar:3.4.7:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-json:jar:3.4.7:compile
[INFO] |  |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.18.4:compile
[INFO] |  |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.4:compile
[INFO] |  |  |  \- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.18.4:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-reactor-netty:jar:3.4.7:compile
[INFO] |  |  |  \- io.projectreactor.netty:reactor-netty-http:jar:1.2.7:compile
[INFO] |  |  |     +- io.netty:netty-codec-http:jar:4.1.122.Final:compile
[INFO] |  |  |     +- io.netty:netty-codec-http2:jar:4.1.122.Final:compile
[INFO] |  |  |     +- io.netty:netty-resolver-dns:jar:4.1.122.Final:compile
[INFO] |  |  |     |  \- io.netty:netty-codec-dns:jar:4.1.122.Final:compile
[INFO] |  |  |     +- io.netty:netty-resolver-dns-native-macos:jar:osx-x86_64:4.1.122.Final:compile
[INFO] |  |  |     |  \- io.netty:netty-resolver-dns-classes-macos:jar:4.1.122.Final:compile
[INFO] |  |  |     +- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.122.Final:compile
[INFO] |  |  |     |  \- io.netty:netty-transport-classes-epoll:jar:4.1.122.Final:compile
[INFO] |  |  |     \- io.projectreactor.netty:reactor-netty-core:jar:1.2.7:compile
[INFO] |  |  |        \- io.netty:netty-handler-proxy:jar:4.1.122.Final:compile
[INFO] |  |  |           \- io.netty:netty-codec-socks:jar:4.1.122.Final:compile
[INFO] |  |  +- org.springframework:spring-web:jar:6.2.8:compile
[INFO] |  |  \- org.springframework:spring-webflux:jar:6.2.8:compile
[INFO] |  \- org.springframework.boot:spring-boot-starter-actuator:jar:3.4.7:compile
[INFO] |     +- org.springframework.boot:spring-boot-actuator-autoconfigure:jar:3.4.7:compile
[INFO] |     |  \- org.springframework.boot:spring-boot-actuator:jar:3.4.7:compile
[INFO] |     +- io.micrometer:micrometer-observation:jar:1.14.8:compile
[INFO] |     |  \- io.micrometer:micrometer-commons:jar:1.14.8:compile
[INFO] |     \- io.micrometer:micrometer-jakarta9:jar:1.14.8:compile
[INFO] +- org.springframework.cloud:spring-cloud-starter-gateway:jar:4.2.1:compile
[INFO] |  +- org.springframework.cloud:spring-cloud-starter:jar:4.2.1:compile
[INFO] |  |  \- org.bouncycastle:bcprov-jdk18on:jar:1.78.1:compile
[INFO] |  \- org.springframework.cloud:spring-cloud-gateway-server:jar:4.2.1:compile
[INFO] |     \- io.projectreactor.addons:reactor-extra:jar:3.5.2:compile
[INFO] +- com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery:jar:2023.0.3.3:compile
[INFO] |  +- com.alibaba.cloud:spring-cloud-alibaba-commons:jar:2023.0.3.3:compile
[INFO] |  +- com.alibaba.nacos:nacos-client:jar:2.4.2:compile
[INFO] |  |  +- com.alibaba.nacos:nacos-auth-plugin:jar:2.4.2:compile
[INFO] |  |  +- com.alibaba.nacos:nacos-encryption-plugin:jar:2.4.2:compile
[INFO] |  |  +- com.alibaba.nacos:nacos-logback-adapter-12:jar:2.4.2:compile
[INFO] |  |  +- com.alibaba.nacos:logback-adapter:jar:1.1.3:compile
[INFO] |  |  +- com.alibaba.nacos:nacos-log4j2-adapter:jar:2.4.2:compile
[INFO] |  |  +- commons-codec:commons-codec:jar:1.17.2:compile
[INFO] |  |  +- com.fasterxml.jackson.core:jackson-core:jar:2.18.4.1:compile
[INFO] |  |  +- org.apache.httpcomponents:httpasyncclient:jar:4.1.5:compile
[INFO] |  |  |  +- org.apache.httpcomponents:httpcore-nio:jar:4.4.16:compile
[INFO] |  |  |  \- org.apache.httpcomponents:httpclient:jar:4.5.13:compile
[INFO] |  |  +- org.apache.httpcomponents:httpcore:jar:4.4.16:compile
[INFO] |  |  +- io.prometheus:simpleclient:jar:0.16.0:compile
[INFO] |  |  |  +- io.prometheus:simpleclient_tracer_otel:jar:0.16.0:compile
[INFO] |  |  |  |  \- io.prometheus:simpleclient_tracer_common:jar:0.16.0:compile
[INFO] |  |  |  \- io.prometheus:simpleclient_tracer_otel_agent:jar:0.16.0:compile
[INFO] |  |  +- org.yaml:snakeyaml:jar:2.3:compile
[INFO] |  |  \- io.micrometer:micrometer-core:jar:1.14.8:compile
[INFO] |  |     +- org.hdrhistogram:HdrHistogram:jar:2.2.2:runtime
[INFO] |  |     \- org.latencyutils:LatencyUtils:jar:2.0.3:runtime
[INFO] |  +- org.springframework.cloud:spring-cloud-commons:jar:4.2.1:compile
[INFO] |  |  \- org.springframework.security:spring-security-crypto:jar:6.4.7:compile
[INFO] |  \- org.springframework.cloud:spring-cloud-context:jar:4.2.1:compile
[INFO] +- com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-config:jar:2023.0.3.3:compile
[INFO] |  +- com.alibaba.cloud:spring-alibaba-nacos-config:jar:2023.0.3.3:compile
[INFO] |  +- org.slf4j:slf4j-api:jar:2.0.17:compile
[INFO] |  \- jakarta.annotation:jakarta.annotation-api:jar:2.1.1:compile
[INFO] +- org.springframework.cloud:spring-cloud-starter-loadbalancer:jar:4.2.1:compile
[INFO] |  +- org.springframework.cloud:spring-cloud-loadbalancer:jar:4.2.1:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-cache:jar:3.4.7:compile
[INFO] |  |  \- org.springframework:spring-context-support:jar:6.2.8:compile
[INFO] |  \- com.stoyanr:evictor:jar:1.0.0:compile
[INFO] +- org.springframework.boot:spring-boot-starter-data-redis-reactive:jar:3.4.7:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter:jar:3.4.7:compile
[INFO] |  |  +- org.springframework.boot:spring-boot:jar:3.4.7:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-autoconfigure:jar:3.4.7:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-starter-logging:jar:3.4.7:compile
[INFO] |  |     +- ch.qos.logback:logback-classic:jar:1.5.18:compile
[INFO] |  |     |  \- ch.qos.logback:logback-core:jar:1.5.18:compile
[INFO] |  |     +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.24.3:compile
[INFO] |  |     |  \- org.apache.logging.log4j:log4j-api:jar:2.24.3:compile
[INFO] |  |     \- org.slf4j:jul-to-slf4j:jar:2.0.17:compile
[INFO] |  +- io.lettuce:lettuce-core:jar:6.4.2.RELEASE:compile
[INFO] |  |  +- io.netty:netty-common:jar:4.1.122.Final:compile
[INFO] |  |  +- io.netty:netty-handler:jar:4.1.122.Final:compile
[INFO] |  |  |  +- io.netty:netty-resolver:jar:4.1.122.Final:compile
[INFO] |  |  |  +- io.netty:netty-buffer:jar:4.1.122.Final:compile
[INFO] |  |  |  +- io.netty:netty-transport-native-unix-common:jar:4.1.122.Final:compile
[INFO] |  |  |  \- io.netty:netty-codec:jar:4.1.122.Final:compile
[INFO] |  |  \- io.netty:netty-transport:jar:4.1.122.Final:compile
[INFO] |  +- io.projectreactor:reactor-core:jar:3.7.7:compile
[INFO] |  |  \- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |  \- org.springframework.data:spring-data-redis:jar:3.4.7:compile
[INFO] |     +- org.springframework.data:spring-data-keyvalue:jar:3.4.7:compile
[INFO] |     |  \- org.springframework.data:spring-data-commons:jar:3.4.7:compile
[INFO] |     +- org.springframework:spring-tx:jar:6.2.8:compile
[INFO] |     \- org.springframework:spring-oxm:jar:6.2.8:compile
[INFO] +- org.springframework.boot:spring-boot-starter-security:jar:3.4.7:compile
[INFO] |  +- org.springframework:spring-aop:jar:6.2.8:compile
[INFO] |  |  \- org.springframework:spring-beans:jar:6.2.8:compile
[INFO] |  +- org.springframework.security:spring-security-config:jar:6.4.7:compile
[INFO] |  |  +- org.springframework.security:spring-security-core:jar:6.4.7:compile
[INFO] |  |  \- org.springframework:spring-context:jar:6.2.8:compile
[INFO] |  \- org.springframework.security:spring-security-web:jar:6.4.7:compile
[INFO] |     \- org.springframework:spring-expression:jar:6.2.8:compile
[INFO] +- io.jsonwebtoken:jjwt-api:jar:0.12.6:compile
[INFO] +- io.jsonwebtoken:jjwt-impl:jar:0.12.6:runtime
[INFO] +- io.jsonwebtoken:jjwt-jackson:jar:0.12.6:runtime
[INFO] |  \- com.fasterxml.jackson.core:jackson-databind:jar:2.18.4:compile
[INFO] |     \- com.fasterxml.jackson.core:jackson-annotations:jar:2.18.4:compile
[INFO] +- org.springframework.boot:spring-boot-starter-test:jar:3.4.7:test
[INFO] |  +- org.springframework.boot:spring-boot-test:jar:3.4.7:test
[INFO] |  +- org.springframework.boot:spring-boot-test-autoconfigure:jar:3.4.7:test
[INFO] |  +- com.jayway.jsonpath:json-path:jar:2.9.0:test
[INFO] |  +- jakarta.xml.bind:jakarta.xml.bind-api:jar:4.0.2:runtime
[INFO] |  |  \- jakarta.activation:jakarta.activation-api:jar:2.1.3:runtime
[INFO] |  +- net.minidev:json-smart:jar:2.5.2:test
[INFO] |  |  \- net.minidev:accessors-smart:jar:2.5.2:test
[INFO] |  |     \- org.ow2.asm:asm:jar:9.7.1:test
[INFO] |  +- org.assertj:assertj-core:jar:3.26.3:test
[INFO] |  |  \- net.bytebuddy:byte-buddy:jar:1.15.11:test
[INFO] |  +- org.awaitility:awaitility:jar:4.2.2:test
[INFO] |  +- org.hamcrest:hamcrest:jar:2.2:test
[INFO] |  +- org.junit.jupiter:junit-jupiter:jar:5.11.4:test
[INFO] |  |  +- org.junit.jupiter:junit-jupiter-api:jar:5.11.4:test
[INFO] |  |  |  +- org.opentest4j:opentest4j:jar:1.3.0:test
[INFO] |  |  |  +- org.junit.platform:junit-platform-commons:jar:1.11.4:test
[INFO] |  |  |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] |  |  +- org.junit.jupiter:junit-jupiter-params:jar:5.11.4:test
[INFO] |  |  \- org.junit.jupiter:junit-jupiter-engine:jar:5.11.4:test
[INFO] |  |     \- org.junit.platform:junit-platform-engine:jar:1.11.4:test
[INFO] |  +- org.mockito:mockito-core:jar:5.14.2:test
[INFO] |  |  +- net.bytebuddy:byte-buddy-agent:jar:1.15.11:test
[INFO] |  |  \- org.objenesis:objenesis:jar:3.3:test
[INFO] |  +- org.mockito:mockito-junit-jupiter:jar:5.14.2:test
[INFO] |  +- org.skyscreamer:jsonassert:jar:1.5.3:test
[INFO] |  |  \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:test
[INFO] |  +- org.springframework:spring-core:jar:6.2.8:compile
[INFO] |  |  \- org.springframework:spring-jcl:jar:6.2.8:compile
[INFO] |  +- org.springframework:spring-test:jar:6.2.8:test
[INFO] |  \- org.xmlunit:xmlunit-core:jar:2.10.2:test
[INFO] \- io.projectreactor:reactor-test:jar:3.7.7:test
[INFO] ------------------------------------------------------------------------
[INFO] BUILD SUCCESS
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  4.984 s
[INFO] Finished at: 2025-07-08T10:04:58+08:00
[INFO] ------------------------------------------------------------------------
