package com.hzwangda.edu.gateway.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.route.RouteDefinition;
import org.springframework.cloud.gateway.route.RouteDefinitionLocator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.config.ResourceHandlerRegistry;
import org.springframework.web.reactive.config.WebFluxConfigurer;

import java.util.ArrayList;
import java.util.List;

/**
 * API文档网站配置
 *
 * <p>配置统一的API文档网站，包括：</p>
 * <ul>
 *   <li>多服务文档聚合</li>
 *   <li>自定义UI主题</li>
 *   <li>文档版本管理</li>
 *   <li>在线测试功能</li>
 *   <li>文档搜索和导航</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-21
 */
@Configuration
public class ApiDocumentationConfig implements WebFluxConfigurer {

    private static final Logger log = LoggerFactory.getLogger(ApiDocumentationConfig.class);

    @Value("${server.port:8080}")
    private String serverPort;

    @Value("${hky.hr.api-docs.title:杭科院人事管理系统API文档}")
    private String apiDocsTitle;

    @Value("${hky.hr.api-docs.version:2.0.0}")
    private String apiDocsVersion;

    @Value("${hky.hr.api-docs.enabled:true}")
    private boolean apiDocsEnabled;

    /**
     * 配置API文档聚合（简化版本，移除SpringDoc依赖）
     */
    @Bean
    public List<String> apiServices(RouteDefinitionLocator locator) {
        List<String> services = new ArrayList<>();

        if (!apiDocsEnabled) {
            log.info("API文档已禁用");
            return services;
        }

        // 获取所有微服务路由
        List<RouteDefinition> definitions = locator.getRouteDefinitions().collectList().block();

        if (definitions != null) {
            definitions.stream()
                    .filter(routeDefinition -> routeDefinition.getId().endsWith("-service"))
                    .forEach(routeDefinition -> {
                        String serviceName = routeDefinition.getId().replace("-service", "");
                        String displayName = getServiceDisplayName(serviceName);

                        services.add(serviceName);
                        log.info("添加API文档服务: {} -> {}", serviceName, displayName);
                    });
        }

        // 添加网关自身
        services.add("gateway");

        log.info("API文档聚合配置完成，共{}个服务", services.size());
        return services;
    }

    /**
     * 配置主OpenAPI文档
     */
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(createMainApiInfo())
                .servers(createServerList());
    }

    /**
     * 配置静态资源处理
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 自定义Swagger UI资源
        registry.addResourceHandler("/swagger-ui/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/swagger-ui/")
                .addResourceLocations("classpath:/static/swagger-ui/");

        // API文档静态资源
        registry.addResourceHandler("/api-docs/**")
                .addResourceLocations("classpath:/static/api-docs/");

        // 品牌资源
        registry.addResourceHandler("/assets/**")
                .addResourceLocations("classpath:/static/assets/");
    }

    /**
     * 创建主API信息
     */
    private Info createMainApiInfo() {
        return new Info()
                .title(apiDocsTitle)
                .description(buildMainDescription())
                .version(apiDocsVersion)
                .contact(new Contact()
                        .name("杭科院人事管理系统开发团队")
                        .email("<EMAIL>")
                        .url("https://hr.hky.edu.cn"))
                .license(new License()
                        .name("杭州科技职业技术学院内部系统许可证")
                        .url("https://hr.hky.edu.cn/license"));
    }

    /**
     * 构建主描述信息
     */
    private String buildMainDescription() {
        return """
                # 杭科院人事管理系统API文档

                欢迎使用杭州科技职业技术学院人事管理系统API文档！

                ## 🚀 系统特性

                - **高性能**: API响应时间P95 < 80ms，支持1000+ TPS并发
                - **高安全**: JWT认证 + RBAC权限控制，数据加密传输
                - **高可用**: 99.9%系统可用性，自动故障恢复
                - **易集成**: RESTful API设计，完整的SDK和示例代码

                ## 📋 服务架构

                系统采用微服务架构，包含以下核心服务：

                | 服务名称 | 端口 | 功能描述 |
                |---------|------|----------|
                | 🏢 组织管理服务 | 8101 | 部门、岗位、编制管理 |
                | 👥 员工信息服务 | 8102 | 员工档案、学历、资质管理 |
                | 🔄 人事变动服务 | 8103 | 调动、晋升、离职管理 |
                | 📋 合同管理服务 | 8104 | 合同签订、续签、终止 |
                | ⏰ 考勤管理服务 | 8105 | 打卡、请假、加班管理 |
                | 💰 薪酬管理服务 | 8106 | 工资、奖金、福利管理 |
                | 📊 绩效评估服务 | 8107 | 绩效考核、评价管理 |
                | 🎓 师资建设服务 | 8108 | 培训、发展、能力评估 |
                | 🔍 招聘管理服务 | 8109 | 职位发布、面试、录用 |
                | 🏆 职称评审服务 | 8110 | 职称申报、评审、管理 |

                ## 🔐 认证说明

                所有API（除登录接口外）都需要JWT认证：

                ```
                Authorization: Bearer <your-jwt-token>
                ```

                ## 📝 响应格式

                统一的响应格式：

                ```json
                {
                  "code": 200,
                  "message": "操作成功",
                  "data": {...},
                  "timestamp": 1640995200000
                }
                ```

                ## 🔗 相关链接

                - [📚 完整文档](https://docs.hky.edu.cn/hr-system)
                - [🎯 快速开始](https://docs.hky.edu.cn/hr-system/quick-start)
                - [💡 最佳实践](https://docs.hky.edu.cn/hr-system/best-practices)
                - [❓ 常见问题](https://docs.hky.edu.cn/hr-system/faq)
                """;
    }

    /**
     * 创建服务器列表
     */
    private List<Server> createServerList() {
        List<Server> servers = new ArrayList<>();

        servers.add(new Server()
                .url("http://localhost:" + serverPort)
                .description("🔧 本地开发环境"));

        servers.add(new Server()
                .url("https://dev-api.hky.edu.cn")
                .description("🚧 开发环境"));

        servers.add(new Server()
                .url("https://test-api.hky.edu.cn")
                .description("🧪 测试环境"));

        servers.add(new Server()
                .url("https://api.hky.edu.cn")
                .description("🚀 生产环境"));

        return servers;
    }



    /**
     * 获取服务显示名称
     */
    private String getServiceDisplayName(String serviceName) {
        return switch (serviceName) {
            case "organization" -> "🏢 组织管理";
            case "employee" -> "👥 员工信息";
            case "personnel-change" -> "🔄 人事变动";
            case "contract" -> "📋 合同管理";
            case "attendance" -> "⏰ 考勤管理";
            case "salary" -> "💰 薪酬管理";
            case "appraisal" -> "📊 绩效评估";
            case "faculty-development" -> "🎓 师资建设";
            case "recruitment" -> "🔍 招聘管理";
            case "title-evaluation" -> "🏆 职称评审";
            default -> "📦 " + serviceName.toUpperCase();
        };
    }
}