package com.hzwangda.edu.gateway.config;

import jakarta.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.springdoc.core.models.GroupedOpenApi;
import org.springdoc.core.properties.AbstractSwaggerUiConfigProperties;
import org.springdoc.core.properties.SwaggerUiConfigParameters;
import org.springdoc.core.properties.SwaggerUiConfigProperties;
import org.springdoc.core.utils.Constants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.handler.predicate.PredicateDefinition;
import org.springframework.cloud.gateway.route.RouteDefinition;
import org.springframework.cloud.gateway.route.RouteDefinitionLocator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Swagger聚合配置类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Configuration
public class SwaggerConfig {

    @Autowired
    private SwaggerUiConfigProperties swaggerUiConfigProperties;
    @Autowired
    private RouteDefinitionLocator locator;

    /**
     * 配置Swagger UI参数
     */
    @PostConstruct
    public void apis() {
        //获取所有的路径配置
        List<RouteDefinition> definitions = locator.getRouteDefinitions().collectList().block();
        //过滤，只要lb模块式
        List<RouteDefinition> serviceRoutes = definitions.stream().filter(route -> null != route.getUri() && route.getUri().getScheme().equals("lb")).collect(Collectors.toList());
        //按根据PredicateDefinition参数值和/** 确定是否路径匹配
        Set<AbstractSwaggerUiConfigProperties.SwaggerUrl> lbRouteUrl = new HashSet<>();
        Optional.ofNullable(serviceRoutes).orElse(Collections.emptyList()).forEach(route -> {
            AbstractSwaggerUiConfigProperties.SwaggerUrl swaggerUrl = new AbstractSwaggerUiConfigProperties.SwaggerUrl();
            //获取路径前缀
            List<PredicateDefinition> predicates = route.getPredicates();
            if (null == predicates || predicates.size() <= 0) {
                return;
            }
            String prefix = "";
            for (PredicateDefinition predicate : predicates) {
                String predicateName = predicate.getName();
                if ("path".equalsIgnoreCase(predicateName)) {
                    for (String regex : predicate.getArgs().values()) {
                        if (regex.endsWith("/**")) {
                            prefix = regex.substring(0, regex.length() - 3);
                            continue;
                        }
                    }

                }
            }
            //不是路径匹配的路由，跳过
            if (StringUtils.isBlank(prefix)) {
                return;
            }

            swaggerUrl.setUrl(prefix + Constants.DEFAULT_API_DOCS_URL);
            swaggerUrl.setName(prefix);
            swaggerUrl.setDisplayName(route.getId());
            lbRouteUrl.add(swaggerUrl);
        });

        //添加swaggerUI服务集成匹配
        if (lbRouteUrl.size() > 0) {
            Set<AbstractSwaggerUiConfigProperties.SwaggerUrl> propertiesUrls = swaggerUiConfigProperties.getUrls();
            if (null == propertiesUrls || propertiesUrls.size() <= 0) {
                propertiesUrls = lbRouteUrl;
            } else {
                propertiesUrls.addAll(lbRouteUrl);
            }
            swaggerUiConfigProperties.setUrls(propertiesUrls);
        }
    }

    /**
     * 添加服务分组
     */
    private void addServiceGroup(List<GroupedOpenApi> groups,
                               SwaggerUiConfigParameters swaggerUiConfigParameters,
                               String serviceName, String displayName) {
        swaggerUiConfigParameters.addGroup(serviceName);

        GroupedOpenApi group = GroupedOpenApi.builder()
                .group(serviceName)
                .displayName(displayName)
                .pathsToMatch("/api/v1/" + serviceName + "/**")
                .build();

        groups.add(group);
    }

    /**
     * 网关服务自身的API文档
     */
    @Bean
    public GroupedOpenApi gatewayApi() {
        return GroupedOpenApi.builder()
                .group("gateway")
                .displayName("API网关")
                .pathsToMatch("/gateway/**", "/actuator/**")
                .build();
    }
}
