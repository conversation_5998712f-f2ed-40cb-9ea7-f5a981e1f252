server:
  port: 8000

spring:
  application:
    name: edu-gateway-service
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_SERVER_ADDR:127.0.0.1:8848}
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:nacos}
        namespace: ${NACOS_NAMESPACE:hky-hr}
        group: ${NACOS_GROUP:DEFAULT_GROUP}
      config:
        server-addr: ${NACOS_SERVER_ADDR:localhost:8848}
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:nacos}
        namespace: ${NACOS_NAMESPACE:hky-hr}
        group: ${NACOS_GROUP:DEFAULT_GROUP}
        file-extension: yml
        import-check:
          enabled: false
    gateway:
      discovery:
        locator:
          enabled: true  # 启用服务发现路由
          lower-case-service-id: true  # 服务名小写
      default-filters:
        - DedupeResponseHeader=Access-Control-Allow-Credentials Access-Control-Allow-Origin
      globalcors:
        corsConfigurations:
          '[/**]':
            allowedOriginPatterns: "*"
            allowedMethods:
              - GET
              - POST
              - PUT
              - DELETE
              - OPTIONS
            allowedHeaders: "*"
            allowCredentials: true
            maxAge: 3600
      routes:
        # 认证服务路由
        - id: auth-service
          uri: lb://edu-auth-service
          predicates:
            - Path=/api/auth/**
          filters:
            - StripPrefix=2

        # 文件服务路由
        - id: edu-file-service
          uri: lb://edu-file-service
          predicates:
            - Path=/file-service/**
          filters:
            - StripPrefix=2
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 10
                redis-rate-limiter.burstCapacity: 20
                key-resolver: "#{@userKeyResolver}"


        # 审计服务路由
        - id: audit-service
          uri: lb://edu-audit-service
          predicates:
            - Path=/api/audit/**
          filters:
            - StripPrefix=2

        # 工作流服务路由
        - id: workflow-service
          uri: lb://edu-workflow-service
          predicates:
            - Path=/api/workflow/**
          filters:
            - StripPrefix=2
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 50
                redis-rate-limiter.burstCapacity: 100
                key-resolver: "#{@userKeyResolver}"
            - name: CircuitBreaker
              args:
                name: workflowServiceCircuitBreaker
                fallbackUri: forward:/fallback/workflow

        # 通知服务路由
        - id: notification-service
          uri: lb://edu-notification-service
          predicates:
            - Path=/api/notification/**
          filters:
            - StripPrefix=2
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 100
                redis-rate-limiter.burstCapacity: 200
                key-resolver: "#{@userKeyResolver}"
            - name: CircuitBreaker
              args:
                name: notificationServiceCircuitBreaker
                fallbackUri: forward:/fallback/notification

        # 字典服务路由
        - id: dictionary-service
          uri: lb://edu-dictionary-service
          predicates:
            - Path=/api/dictionary/**
          filters:
            - StripPrefix=2

        # ID服务路由
        - id: id-service
          uri: lb://edu-id-service
          predicates:
            - Path=/api/id/**
          filters:
            - StripPrefix=2

  # Redis配置
  data:
    redis:
      host: ${REDIS_HOST:************}
      port: ${REDIS_PORT:31000}
      password: ${REDIS_PWD:sjyt_cywKZHAl}
      database: ${REDIS_DB:6}
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms
  security:
    basic:
      path: /swagger-ui.html
      enabled: true
    user:
      name: admin #账号
      password: 123456  #密码

# 日志配置
logging:
  level:
    com.hzwangda.edu.gateway: DEBUG
    org.springframework.cloud.gateway: DEBUG
    org.springframework.web.reactive: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,gateway
  endpoint:
    health:
      show-details: always
    gateway:
      enabled: true
  prometheus:
    metrics:
      export:
        enabled: true
  metrics:
    tags:
      application: ${spring.application.name}

# 网关自定义配置
gateway:
  # 认证配置
  auth:
    enabled: true
    excludePaths:
      - /api/auth/login
      - /api/auth/register
      - /api/auth/oauth2/**
      - /actuator/**
      - /swagger-ui.html
      - /swagger-ui/**
      - /v3/api-docs/**
    jwt:
      secret: ${JWT_SECRET:mySecretKey}
      expiration: 86400

  # 限流配置
  ratelimit:
    enabled: true
    default-replenish-rate: 100
    default-burst-capacity: 200

  # 日志配置
  logging:
    enabled: true
    log-headers: true
    log-body: false

  # 动态路由配置
  route:
    config:
      enabled: true
      data-id: gateway-routes
      group: DEFAULT_GROUP

# Seata分布式事务配置（网关服务禁用）
seata:
  enabled: false

# 灰度发布配置
gray-release:
  enabled: true
  strategy: weight  # 默认使用权重策略
  default-version: stable
  gray-version: gray
  rules:
    edu-auth-service:
      enabled: false
      weight: 10
      version: v2.0.0-gray
      user-whitelist:
        - admin
        - test001
      ip-whitelist:
        - 192.168.1.*
        - 10.0.0.*
      header-rules:
        X-Gray-Test: "true"
    edu-file-service:
      enabled: false
      weight: 20
      version: v2.0.0-gray
    edu-workflow-service:
      enabled: false
      weight: 10
      version: v2.0.0-gray